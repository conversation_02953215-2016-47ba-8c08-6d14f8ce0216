#!/usr/bin/env python3
"""
Test rate limit configuration from environment variables.
"""

import os
from src.kline_processor.config.settings import Settings
from src.kline_processor.clients.http_client import HTTPClient

def main():
    print("🔍 Rate Limit Configuration Test")
    print("=" * 50)
    
    # Check environment variable
    rate_limit_env = os.getenv("RATE_LIMIT_RETRY_DELAY", "Not set")
    print(f"Environment variable RATE_LIMIT_RETRY_DELAY: {rate_limit_env}")
    
    # Check settings
    settings = Settings()
    print(f"Settings rate_limit_retry_delay: {settings.rate_limit_retry_delay} seconds")
    print(f"Settings max_retries: {settings.max_retries}")
    
    # Check HTTP client
    http_client = HTTPClient()
    print(f"\nHTTP Client Configuration:")
    print(f"  - max_retries: {http_client.max_retries}")
    print(f"  - rate_limit_retry_delay: {http_client.rate_limit_retry_delay} seconds")
    print(f"  - backoff_factor: {http_client.backoff_factor}")
    
    # Calculate total wait time for 429 errors
    total_wait = 0
    print(f"\n429 Error Retry Strategy (with {http_client.max_retries} retries):")
    for attempt in range(http_client.max_retries):
        wait_time = http_client.rate_limit_retry_delay
        total_wait += wait_time
        print(f"  - Retry {attempt + 1}: wait {wait_time}s (total: {total_wait}s)")
    
    print(f"\nMaximum total wait time for 429 errors: {total_wait} seconds ({total_wait/60:.1f} minutes)")
    
    # Show configuration for other errors (500+)
    print(f"\nServer Error (500+) Retry Strategy:")
    total_wait = 0
    for attempt in range(http_client.max_retries):
        wait_time = http_client.backoff_factor * (2 ** attempt)
        total_wait += wait_time
        print(f"  - Retry {attempt + 1}: wait {wait_time}s (total: {total_wait:.1f}s)")

if __name__ == "__main__":
    main()