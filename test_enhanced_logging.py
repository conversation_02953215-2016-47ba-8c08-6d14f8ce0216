#!/usr/bin/env python3
"""
Test script for the enhanced logging system.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kline_processor.config.settings import Settings
from kline_processor.utils.enhanced_logger import setup_enhanced_logging, SyncLogger
from kline_processor.services.historical_data_service import HistoricalDataService
from kline_processor.clients.exchange_api_client_v2 import ExchangeAPIClientV2
from kline_processor.storage.kline_storage import KlineStorage
from kline_processor.storage.redis_client import RedisClient

async def test_enhanced_logging():
    """Test the enhanced logging system with a small sync operation."""
    
    print("🧪 Testing Enhanced Logging System")
    print("=" * 50)
    
    # Load settings
    settings = Settings()
    print(f"📁 Log directory: {settings.log_dir}")
    print(f"📄 Console level: {settings.log_console_level}")
    print(f"📄 File level: {settings.log_file_level}")
    
    # Setup enhanced logging
    main_logger, sync_logger = setup_enhanced_logging(settings)
    
    print(f"✅ Enhanced logging system initialized")
    print(f"📂 Logs will be saved to: {Path(settings.log_dir).absolute()}")
    
    # Test basic logging
    main_logger.info("🚀 Starting enhanced logging test")
    main_logger.debug("🔍 This is a debug message (should only appear in file)")
    main_logger.warning("⚠️ This is a warning message")
    main_logger.error("❌ This is an error message (should appear in console)")
    
    # Test sync logging
    print("\n🔄 Testing sync logging with small data fetch...")
    
    try:
        # Initialize components
        redis_client = RedisClient()
        await redis_client.connect()
        
        storage = KlineStorage(redis_client)
        api_client = ExchangeAPIClientV2(settings=settings)
        
        # Create historical service
        historical_service = HistoricalDataService(
            exchange_client=api_client,
            storage=storage,
            settings=settings
        )
        
        # Test with small sync (3 iterations of 10 records each)
        main_logger.info("🎯 Starting test sync: BTCUSDT 1m, 3 iterations of 10 records")
        
        result = await historical_service.sync_klines(
            symbol="BTCUSDT",
            interval="1m",
            target_count=10,  # Small batch for testing
            count=3  # Only 3 iterations
        )
        
        main_logger.info(f"✅ Test sync completed: {result.klines_fetched} records fetched")
        
        # Close connections
        await api_client.close()
        await redis_client.disconnect()
        
    except Exception as e:
        main_logger.error(f"❌ Test failed: {e}")
        import traceback
        main_logger.error(f"Traceback: {traceback.format_exc()}")
    
    print("\n📊 Test Results:")
    print("- Check console output: Should only show ERROR level and above")
    print("- Check log files in logs/ directory for detailed logs")
    print("- Sync operations should have detailed progress tracking")
    
    # Show log directory contents
    log_dir = Path(settings.log_dir)
    if log_dir.exists():
        print(f"\n📁 Log files created:")
        for log_file in log_dir.rglob("*.log"):
            size = log_file.stat().st_size
            print(f"  📄 {log_file.relative_to(log_dir)} ({size} bytes)")
    
    print("\n✅ Enhanced logging test completed!")

if __name__ == "__main__":
    asyncio.run(test_enhanced_logging())
