#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to check configuration values and debug the sync issue.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kline_processor.config.settings import Settings

def check_config():
    """Check configuration values."""
    
    print("🔧 Loading configuration...")
    settings = Settings()
    
    print(f"\n📊 Configuration values:")
    print(f"  STARTUP_BACKFILL_TARGET_KLINES: {settings.startup_backfill_target_klines}")
    print(f"  STARTUP_BACKFILL_ITERATIONS: {settings.startup_backfill_iterations}")
    print(f"  Expected total K-lines per symbol/interval: {settings.startup_backfill_target_klines * settings.startup_backfill_iterations}")
    
    print(f"\n📈 Other relevant settings:")
    print(f"  SYMBOLS: {settings.symbols_list}")
    print(f"  WEBSOCKET_INTERVALS: {settings.websocket_intervals_list}")
    print(f"  HISTORICAL_BATCH_SIZE: {settings.historical_batch_size}")
    print(f"  HISTORICAL_RATE_LIMIT_DELAY: {settings.historical_rate_limit_delay}")
    
    print(f"\n🔍 Environment variables:")
    print(f"  STARTUP_BACKFILL_TARGET_KLINES: {os.getenv('STARTUP_BACKFILL_TARGET_KLINES', 'Not set')}")
    print(f"  STARTUP_BACKFILL_ITERATIONS: {os.getenv('STARTUP_BACKFILL_ITERATIONS', 'Not set')}")

if __name__ == "__main__":
    check_config()
