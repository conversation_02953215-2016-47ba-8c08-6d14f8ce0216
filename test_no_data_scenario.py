#!/usr/bin/env python3
"""
Test script to simulate no data scenarios.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kline_processor.services.historical_data_service import HistoricalDataService
from kline_processor.storage.redis_client import RedisClient
from kline_processor.storage.kline_storage import KlineStorage
from kline_processor.clients.exchange_api_client_v2 import ExchangeAPIClientV2


async def test_no_data_scenarios():
    """Test various no data scenarios."""
    
    redis_client = RedisClient()
    storage = KlineStorage(redis_client)
    exchange_client = ExchangeAPIClientV2()
    
    try:
        # Connect to Redis
        await redis_client.connect()
        print("✅ Connected to Redis")
        
        # Create historical data service
        historical_service = HistoricalDataService(
            storage=storage,
            exchange_client=exchange_client
        )
        
        print("\n🔍 Testing scenarios where API might return no data...")
        
        # Test 1: Request data from very old time (should return empty)
        print("\n📅 Test 1: Requesting very old data (year 2000)")
        try:
            # Use a very old endTime that should have no data
            old_timestamp = 946684800000  # Jan 1, 2000
            klines = await exchange_client.get_continuous_klines(
                symbol="BTCUSDT",
                interval="1m",
                limit=100,
                end_time=old_timestamp
            )
            print(f"  Result: {len(klines)} klines returned")
            if len(klines) == 0:
                print("  ✅ API correctly returns empty list for old data")
            else:
                print(f"  ⚠️  Unexpected: got {len(klines)} klines for old timestamp")
                # Show first few
                for i, kline in enumerate(klines[:3]):
                    print(f"    [{i}]: timestamp={kline.t}")
        except Exception as e:
            print(f"  ❌ Error requesting old data: {e}")
        
        # Test 2: Request data from future time (should return empty)
        print("\n🔮 Test 2: Requesting future data")
        try:
            # Use a future timestamp
            future_timestamp = 2000000000000  # Year 2033
            klines = await exchange_client.get_continuous_klines(
                symbol="BTCUSDT",
                interval="1m",
                limit=100,
                end_time=future_timestamp
            )
            print(f"  Result: {len(klines)} klines returned")
            if len(klines) == 0:
                print("  ✅ API correctly returns empty list for future data")
            else:
                print(f"  ⚠️  Unexpected: got {len(klines)} klines for future timestamp")
        except Exception as e:
            print(f"  ❌ Error requesting future data: {e}")
        
        # Test 3: Test historical sync with very old data
        print("\n🏛️  Test 3: Historical sync with old endTime")
        try:
            result = await historical_service.sync_klines(
                symbol="BTCUSDT",
                interval="1m",
                target_count=1500,
                count=1  # Just 1 iteration
            )
            print(f"  Sync result:")
            print(f"    Success: {result.success}")
            print(f"    Fetched: {result.klines_fetched}")
            print(f"    Error: {result.error}")
            
            # Check what's actually stored
            stored_count = await storage.get_kline_count("BTCUSDT", "1m")
            print(f"    Stored in Redis: {stored_count}")
            
        except Exception as e:
            print(f"  ❌ Error in historical sync: {e}")
        
        # Test 4: Test what happens when we exhaust all available data
        print("\n🔚 Test 4: Simulating data exhaustion scenario")
        print("  (This would happen if we keep requesting older data until none exists)")
        
        # Get current oldest data timestamp
        all_klines = await storage.get_klines("BTCUSDT", "1m")
        if all_klines:
            oldest_timestamp = min(kline.t for kline in all_klines)
            print(f"  Current oldest timestamp in storage: {oldest_timestamp}")
            
            # Try to get data older than our oldest
            try:
                very_old_klines = await exchange_client.get_continuous_klines(
                    symbol="BTCUSDT",
                    interval="1m",
                    limit=100,
                    end_time=oldest_timestamp - 86400000  # 1 day before oldest
                )
                print(f"  Requesting data 1 day before oldest: {len(very_old_klines)} klines")
                
                if len(very_old_klines) == 0:
                    print("  ✅ No more historical data available - this is expected")
                else:
                    print(f"  📊 Found {len(very_old_klines)} older klines")
                    
            except Exception as e:
                print(f"  ❌ Error requesting older data: {e}")
        
        # Test 5: Test invalid symbol
        print("\n❌ Test 5: Invalid symbol")
        try:
            invalid_klines = await exchange_client.get_continuous_klines(
                symbol="INVALIDCOIN",
                interval="1m",
                limit=100
            )
            print(f"  Result for invalid symbol: {len(invalid_klines)} klines")
        except Exception as e:
            print(f"  ✅ Expected error for invalid symbol: {e}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        await exchange_client.close()
        await redis_client.disconnect()
        print("\n✅ Test completed")


if __name__ == "__main__":
    asyncio.run(test_no_data_scenarios())
