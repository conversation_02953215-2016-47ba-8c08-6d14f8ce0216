#!/usr/bin/env python3
"""
<PERSON>ript to clear Redis data for testing.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kline_processor.storage.redis_client import RedisClient


async def clear_redis_data():
    """Clear Redis data for testing."""
    
    redis_client = RedisClient()
    
    try:
        # Connect to Redis
        await redis_client.connect()
        print("✅ Connected to Redis")
        
        # Clear all kline data
        print("\n🧹 Clearing all K-line data...")
        
        # Delete all kline keys
        kline_keys = await redis_client.keys("kline:*")
        if kline_keys:
            deleted = await redis_client.delete(*kline_keys)
            print(f"  Deleted {deleted} kline keys")
        
        # Delete all latest keys
        latest_keys = await redis_client.keys("latest:*")
        if latest_keys:
            deleted = await redis_client.delete(*latest_keys)
            print(f"  Deleted {deleted} latest keys")
        
        # Delete all meta keys
        meta_keys = await redis_client.keys("meta:*")
        if meta_keys:
            deleted = await redis_client.delete(*meta_keys)
            print(f"  Deleted {deleted} meta keys")
        
        # Delete sync and ws keys
        sync_keys = await redis_client.keys("kline:sync:*")
        if sync_keys:
            deleted = await redis_client.delete(*sync_keys)
            print(f"  Deleted {deleted} sync keys")
        
        ws_keys = await redis_client.keys("kline:ws:*")
        if ws_keys:
            deleted = await redis_client.delete(*ws_keys)
            print(f"  Deleted {deleted} ws keys")
        
        print("\n✅ Redis data cleared successfully")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Disconnect
        await redis_client.disconnect()
        print("✅ Disconnected from Redis")


if __name__ == "__main__":
    asyncio.run(clear_redis_data())
