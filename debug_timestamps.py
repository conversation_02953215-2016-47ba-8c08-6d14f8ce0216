#!/usr/bin/env python3
"""
Script to debug timestamp overlaps in the sync process.
"""

import asyncio
import sys
import os
from collections import defaultdict

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kline_processor.storage.redis_client import RedisClient
from kline_processor.storage.kline_storage import KlineStorage

async def debug_timestamps():
    """Debug timestamp distribution in Redis data."""
    
    redis_client = RedisClient()
    storage = KlineStorage(redis_client)
    
    try:
        # Connect to Redis
        await redis_client.connect()
        print("✅ Connected to Redis")
        
        # Get all data with timestamps
        symbol = "BTCUSDT"
        interval = "1m"
        key = f"kline:{symbol}:{interval}"
        
        print(f"\n🔍 Analyzing timestamps in {key}...")
        
        # Get all data with scores (timestamps)
        all_data = await redis_client.zrange(key, 0, -1, withscores=True)
        print(f"Total items in Redis: {len(all_data)}")
        
        if len(all_data) == 0:
            print("No data found!")
            return
            
        # Analyze timestamps
        timestamps = [int(score) for _, score in all_data]
        timestamps.sort()
        
        print(f"\n📊 Timestamp Analysis:")
        print(f"  First timestamp: {timestamps[0]} ({timestamps[0]/1000})")
        print(f"  Last timestamp: {timestamps[-1]} ({timestamps[-1]/1000})")
        print(f"  Time span: {(timestamps[-1] - timestamps[0])/1000/60:.1f} minutes")
        print(f"  Expected count (1min intervals): {(timestamps[-1] - timestamps[0])//60000 + 1}")
        print(f"  Actual count: {len(timestamps)}")
        
        # Check for gaps
        gaps = []
        for i in range(1, len(timestamps)):
            expected_next = timestamps[i-1] + 60000  # 1 minute = 60000ms
            actual_next = timestamps[i]
            if actual_next != expected_next:
                gap_minutes = (actual_next - expected_next) / 60000
                gaps.append({
                    'after': timestamps[i-1],
                    'before': actual_next,
                    'gap_minutes': gap_minutes
                })
        
        print(f"\n🕳️  Found {len(gaps)} gaps:")
        for i, gap in enumerate(gaps[:10]):  # Show first 10 gaps
            print(f"  Gap {i+1}: {gap['gap_minutes']:.1f} minutes between {gap['after']} and {gap['before']}")
        
        if len(gaps) > 10:
            print(f"  ... and {len(gaps) - 10} more gaps")
            
        # Check for duplicates
        timestamp_counts = defaultdict(int)
        for ts in timestamps:
            timestamp_counts[ts] += 1
            
        duplicates = {ts: count for ts, count in timestamp_counts.items() if count > 1}
        print(f"\n🔄 Duplicate timestamps: {len(duplicates)}")
        if duplicates:
            for ts, count in list(duplicates.items())[:5]:
                print(f"  {ts}: {count} times")
                
        # Show sample of recent data
        print(f"\n📋 Last 10 timestamps:")
        for i, ts in enumerate(timestamps[-10:]):
            print(f"  {len(timestamps)-10+i}: {ts}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Disconnect
        await redis_client.disconnect()
        print("\n✅ Disconnected from Redis")

if __name__ == "__main__":
    asyncio.run(debug_timestamps())
