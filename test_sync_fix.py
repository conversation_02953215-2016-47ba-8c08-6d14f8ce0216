#!/usr/bin/env python3
"""
Test script to verify the sync fix.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kline_processor.services.historical_data_service import HistoricalDataService
from kline_processor.storage.redis_client import RedisClient
from kline_processor.storage.kline_storage import KlineStorage
from kline_processor.clients.exchange_api_client_v2 import ExchangeAPIClientV2


async def test_sync_fix():
    """Test the sync fix with a small number of iterations."""
    
    redis_client = RedisClient()
    storage = KlineStorage(redis_client)
    exchange_client = ExchangeAPIClientV2()
    
    try:
        # Connect to Redis
        await redis_client.connect()
        print("✅ Connected to Redis")
        
        # Create historical data service
        historical_service = HistoricalDataService(
            storage=storage,
            exchange_client=exchange_client
        )
        
        # Test with just 2 iterations to see if the logic works
        print("\n🔄 Testing sync with 2 iterations...")
        result = await historical_service.sync_klines(
            symbol="BTCUSDT",
            interval="1m",
            target_count=3000,  # 2 * 1500
            count=2  # Only 2 iterations for testing
        )
        
        print(f"\n📊 Sync result:")
        print(f"  Success: {result.success}")
        print(f"  Fetched: {result.klines_fetched}")
        print(f"  Error: {result.error}")
        
        # Check storage
        count = await storage.get_kline_count("BTCUSDT", "1m")
        print(f"  Stored in Redis: {count}")
        
        # Get some sample data to verify timestamps
        print(f"\n📋 Sample stored data:")
        klines = await storage.get_klines("BTCUSDT", "1m", limit=5)
        for i, kline in enumerate(klines):
            print(f"  [{i}]: timestamp={kline.t}, open={kline.o}, close={kline.c}")
        
        # Get last few items
        print(f"\n📋 Last few stored items:")
        all_klines = await storage.get_klines("BTCUSDT", "1m")
        for i, kline in enumerate(all_klines[-5:]):
            print(f"  [-{5-i}]: timestamp={kline.t}, open={kline.o}, close={kline.c}")
        
        # Check for duplicates
        timestamps = [kline.t for kline in all_klines]
        unique_timestamps = set(timestamps)
        print(f"\n🔍 Duplicate check:")
        print(f"  Total K-lines: {len(all_klines)}")
        print(f"  Unique timestamps: {len(unique_timestamps)}")
        print(f"  Duplicates: {len(timestamps) - len(unique_timestamps)}")
        
        if len(timestamps) != len(unique_timestamps):
            print("  ⚠️  Found duplicates!")
            # Find duplicates
            seen = set()
            duplicates = set()
            for ts in timestamps:
                if ts in seen:
                    duplicates.add(ts)
                seen.add(ts)
            print(f"  Duplicate timestamps: {list(duplicates)[:10]}")  # Show first 10
        else:
            print("  ✅ No duplicates found!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        await exchange_client.close()
        await redis_client.disconnect()
        print("\n✅ Test completed")


if __name__ == "__main__":
    asyncio.run(test_sync_fix())
