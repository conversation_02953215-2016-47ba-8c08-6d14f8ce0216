#!/usr/bin/env python3
"""
Script to check Redis data storage and analyze the K-line sync issue.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kline_processor.storage.redis_client import RedisClient
from kline_processor.storage.kline_storage import KlineStorage


async def check_redis_data():
    """Check Redis data and analyze the storage issue."""
    
    redis_client = RedisClient()
    storage = KlineStorage(redis_client)
    
    try:
        # Connect to Redis
        await redis_client.connect()
        print("✅ Connected to Redis")
        
        # Get storage statistics
        print("\n📊 Getting storage statistics...")
        stats = await storage.get_storage_stats()
        
        print(f"\n📈 Storage Statistics:")
        print(f"  Total symbols: {stats['total_symbols']}")
        print(f"  Total intervals: {stats['total_intervals']}")
        print(f"  Total K-lines: {stats['total_klines']}")
        
        # Check specific symbol data
        symbol = "BTCUSDT"
        interval = "1m"
        
        print(f"\n🔍 Checking {symbol}:{interval} data:")
        
        # Get K-line count
        count = await storage.get_kline_count(symbol, interval)
        print(f"  K-line count: {count}")
        
        # Get latest K-line
        latest = await storage.get_latest_kline(symbol, interval)
        if latest:
            print(f"  Latest K-line timestamp: {latest.t}")
            print(f"  Latest K-line close time: {latest.T}")
            print(f"  Latest K-line is closed: {latest.x}")
        else:
            print("  No latest K-line found")
        
        # Check Redis keys
        print(f"\n🔑 Checking Redis keys:")
        kline_key = storage._get_kline_key(symbol, interval)
        latest_key = storage._get_latest_key(symbol, interval)
        meta_key = storage._get_meta_key(symbol, interval)
        
        print(f"  K-line key: {kline_key}")
        print(f"  Latest key: {latest_key}")
        print(f"  Meta key: {meta_key}")
        
        # Check if keys exist
        exists = await redis_client.exists(kline_key, latest_key, meta_key)
        print(f"  Keys exist: {exists}/3")
        
        # Get sample data with timestamps
        print(f"\n📋 Sample data from {kline_key} (with timestamps):")
        sample_data = await redis_client.zrange(kline_key, 0, 4, withscores=True)  # First 5 items
        for i, (item, score) in enumerate(sample_data):
            import json
            data = json.loads(item)
            timestamp = int(score)
            print(f"  [{i}]: timestamp={timestamp}, open_time={data.get('ot')}, data={item[:80]}...")

        # Get last few items with timestamps
        print(f"\n📋 Last few items from {kline_key} (with timestamps):")
        last_data = await redis_client.zrange(kline_key, -5, -1, withscores=True)  # Last 5 items
        for i, (item, score) in enumerate(last_data):
            import json
            data = json.loads(item)
            timestamp = int(score)
            print(f"  [-{5-i}]: timestamp={timestamp}, open_time={data.get('ot')}, data={item[:80]}...")
        
        # Check all symbols and intervals
        print(f"\n🌍 All stored data:")
        if 'symbol_stats' in stats:
            for symbol, intervals in stats['symbol_stats'].items():
                print(f"  {symbol}:")
                for interval, data in intervals.items():
                    print(f"    {interval}: {data['kline_count']} K-lines")
        
        # Check for any sync-related keys
        print(f"\n🔄 Checking sync-related keys:")
        sync_keys = await redis_client.keys("kline:sync:*")
        print(f"  Sync keys found: {len(sync_keys)}")
        for key in sync_keys[:10]:  # Show first 10
            print(f"    {key}")
        
        ws_keys = await redis_client.keys("kline:ws:*")
        print(f"  WebSocket keys found: {len(ws_keys)}")
        for key in ws_keys[:10]:  # Show first 10
            print(f"    {key}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Disconnect
        await redis_client.disconnect()
        print("\n✅ Disconnected from Redis")


if __name__ == "__main__":
    asyncio.run(check_redis_data())
