#!/usr/bin/env python3
"""
测试数据获取逻辑 - 每次获取10条数据，执行100次，打印所有时间戳验证连续性
"""

import asyncio
import sys
import os
from typing import List, Optional

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kline_processor.clients.exchange_api_client_v2 import ExchangeAPIClientV2
from kline_processor.config.settings import Settings

async def test_fetch_logic():
    """测试循环获取数据的逻辑"""

    # 初始化客户端
    settings = Settings()
    print(f"🔧 API URL: {settings.kline_url_v2}")
    client = ExchangeAPIClientV2(settings=settings)
    
    # 测试参数
    symbol = "BTCUSDT"
    interval = "1m"
    limit = 10  # 每次获取10条数据
    iterations = 100  # 执行100次
    
    print(f"🧪 测试数据获取逻辑")
    print(f"   交易对: {symbol}")
    print(f"   时间间隔: {interval}")
    print(f"   每次获取: {limit} 条")
    print(f"   总迭代次数: {iterations} 次")
    print(f"   预期总数据: {limit * iterations} 条")
    print("=" * 60)
    
    all_timestamps = []
    end_time = None
    
    try:
        for iteration in range(1, iterations + 1):
            print(f"\n🔄 第 {iteration}/{iterations} 次迭代")
            
            # 获取数据
            klines = await client.get_continuous_klines(
                symbol=symbol,
                interval=interval,
                limit=limit,
                end_time=end_time
            )
            
            if not klines or len(klines) == 0:
                print(f"❌ 第 {iteration} 次迭代没有获取到数据，停止")
                break
                
            print(f"✅ 获取到 {len(klines)} 条数据")
            
            # 提取时间戳
            iteration_timestamps = []
            for i, kline in enumerate(klines):
                timestamp = kline.t
                iteration_timestamps.append(timestamp)
                all_timestamps.append(timestamp)
                
            # 打印这次迭代的时间戳范围
            if iteration_timestamps:
                oldest = min(iteration_timestamps)
                newest = max(iteration_timestamps)
                print(f"   时间戳范围: {oldest} 到 {newest}")
                print(f"   最旧数据: {oldest} (索引0)")
                print(f"   最新数据: {newest} (索引{len(iteration_timestamps)-1})")
                
                # 设置下次迭代的end_time（使用最旧的时间戳-1）
                end_time = oldest - 1
                print(f"   下次end_time: {end_time}")
                
                # 检查这次迭代内部的连续性
                sorted_timestamps = sorted(iteration_timestamps)
                gaps_in_iteration = []
                for i in range(1, len(sorted_timestamps)):
                    expected = sorted_timestamps[i-1] + 60000  # 1分钟 = 60000ms
                    actual = sorted_timestamps[i]
                    if actual != expected:
                        gap_minutes = (actual - expected) / 60000
                        gaps_in_iteration.append(gap_minutes)
                
                if gaps_in_iteration:
                    print(f"   ⚠️  本次迭代内发现 {len(gaps_in_iteration)} 个时间间隔异常")
                else:
                    print(f"   ✅ 本次迭代内时间戳连续")
            
            # 每10次迭代暂停一下
            if iteration % 10 == 0:
                print(f"\n📊 已完成 {iteration} 次迭代，累计获取 {len(all_timestamps)} 条数据")
                await asyncio.sleep(0.1)  # 避免请求过快
                
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await client.close()
    
    # 分析所有时间戳
    print("\n" + "=" * 60)
    print("📊 最终数据分析")
    print(f"总共获取数据: {len(all_timestamps)} 条")
    
    if all_timestamps:
        # 排序所有时间戳
        sorted_timestamps = sorted(all_timestamps)
        
        print(f"时间戳范围: {sorted_timestamps[0]} 到 {sorted_timestamps[-1]}")
        print(f"时间跨度: {(sorted_timestamps[-1] - sorted_timestamps[0])/60000:.1f} 分钟")
        
        # 检查重复
        unique_timestamps = set(all_timestamps)
        duplicates = len(all_timestamps) - len(unique_timestamps)
        print(f"唯一时间戳: {len(unique_timestamps)} 个")
        print(f"重复时间戳: {duplicates} 个")
        
        # 检查连续性
        gaps = []
        for i in range(1, len(sorted_timestamps)):
            expected = sorted_timestamps[i-1] + 60000
            actual = sorted_timestamps[i]
            if actual != expected:
                gap_minutes = (actual - expected) / 60000
                gaps.append({
                    'index': i,
                    'after': sorted_timestamps[i-1],
                    'before': actual,
                    'gap_minutes': gap_minutes
                })
        
        print(f"时间间隔异常: {len(gaps)} 个")
        
        # 显示前10个异常
        if gaps:
            print("\n🕳️  时间间隔异常详情 (前10个):")
            for i, gap in enumerate(gaps[:10]):
                print(f"  {i+1}. 索引 {gap['index']}: {gap['after']} -> {gap['before']} (间隔 {gap['gap_minutes']:.1f} 分钟)")
        
        # 显示前20个和后20个时间戳
        print(f"\n📋 前20个时间戳:")
        for i, ts in enumerate(sorted_timestamps[:20]):
            print(f"  {i+1:2d}: {ts}")
            
        print(f"\n📋 后20个时间戳:")
        for i, ts in enumerate(sorted_timestamps[-20:]):
            print(f"  {len(sorted_timestamps)-20+i+1:2d}: {ts}")

if __name__ == "__main__":
    asyncio.run(test_fetch_logic())
