"""
HTTP client with async support, rate limiting, and error handling.

This module provides a comprehensive HTTP client implementation with
connection pooling, retry logic, and rate limiting capabilities.
"""

import asyncio
import time
from typing import Dict, Any, Optional, Union, List
from datetime import datetime, timedelta
import json

import httpx
from httpx import Response, HTTPError, RequestError, TimeoutException

from ..utils.logger import get_logger


class HTTPClientError(Exception):
    """Base exception for HTTP client errors."""
    pass


class RateLimitError(HTTPClientError):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, message: str, retry_after: Optional[int] = None):
        super().__init__(message)
        self.retry_after = retry_after


class HTTPClient:
    """
    Async HTTP client with rate limiting and error handling.
    
    This class provides a high-level interface for making HTTP requests
    with automatic retries, rate limiting, and comprehensive error handling.
    """
    
    def __init__(self, 
                 base_url: str = "",
                 timeout: float = 30.0,
                 max_connections: int = 100,
                 max_keepalive_connections: int = 20,
                 max_retries: int = 3,
                 backoff_factor: float = 0.5,
                 rate_limit_retry_delay: Optional[int] = None):
        """
        Initialize HTTP client.
        
        Args:
            base_url: Base URL for requests
            timeout: Request timeout in seconds
            max_connections: Maximum number of connections
            max_keepalive_connections: Maximum keepalive connections
            max_retries: Maximum number of retries
            backoff_factor: Backoff factor for retries
            rate_limit_retry_delay: Default retry delay for rate limit errors (429)
        """
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.logger = get_logger(self.__class__.__name__)
        
        # Get rate limit delay from settings if not provided
        if rate_limit_retry_delay is None:
            from ..config.settings import Settings
            settings = Settings()
            self.rate_limit_retry_delay = settings.rate_limit_retry_delay
        else:
            self.rate_limit_retry_delay = rate_limit_retry_delay
        
        # Create HTTP client with connection pooling
        limits = httpx.Limits(
            max_connections=max_connections,
            max_keepalive_connections=max_keepalive_connections
        )
        
        self._client = httpx.AsyncClient(
            base_url=base_url,
            timeout=httpx.Timeout(timeout),
            limits=limits,
            headers={
                'User-Agent': 'KlineProcessor/1.0',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate'
            }
        )
        
        # Rate limiting
        self._rate_limit_lock = asyncio.Lock()
        self._last_request_time = 0.0
        self._min_request_interval = 0.1  # 100ms between requests by default
        
        # Request statistics
        self._total_requests = 0
        self._successful_requests = 0
        self._failed_requests = 0
        self._rate_limited_requests = 0
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def close(self):
        """Close the HTTP client and cleanup resources."""
        try:
            await self._client.aclose()
            self.logger.info("HTTP client closed successfully")
        except Exception as e:
            self.logger.error(f"Error closing HTTP client: {e}")
    
    def set_rate_limit(self, requests_per_second: float):
        """
        Set rate limit for requests.
        
        Args:
            requests_per_second: Maximum requests per second
        """
        if requests_per_second <= 0:
            self._min_request_interval = 0
        else:
            self._min_request_interval = 1.0 / requests_per_second
        
        self.logger.info(f"Rate limit set to {requests_per_second} requests/second")
    
    async def _enforce_rate_limit(self):
        """Enforce rate limiting between requests."""
        async with self._rate_limit_lock:
            current_time = time.time()
            time_since_last = current_time - self._last_request_time
            
            if time_since_last < self._min_request_interval:
                sleep_time = self._min_request_interval - time_since_last
                await asyncio.sleep(sleep_time)
            
            self._last_request_time = time.time()
    
    async def _make_request(self,
                          method: str,
                          url: str,
                          params: Optional[Dict[str, Any]] = None,
                          json_data: Optional[Dict[str, Any]] = None,
                          data: Optional[Union[str, bytes, Dict[str, Any]]] = None,
                          headers: Optional[Dict[str, str]] = None,
                          auth: Optional[tuple] = None) -> Response:
        """
        Make HTTP request with retries and error handling.
        
        Args:
            method: HTTP method
            url: Request URL
            params: Query parameters
            json_data: JSON data
            data: Request data
            headers: Additional headers
            auth: Authentication tuple
            
        Returns:
            HTTP response
            
        Raises:
            HTTPClientError: On request failure
            RateLimitError: On rate limit exceeded
        """
        await self._enforce_rate_limit()
        
        # Prepare request parameters
        request_kwargs = {
            'method': method,
            'url': url,
            'params': params,
            'headers': headers,
            'auth': auth
        }
        
        if json_data is not None:
            request_kwargs['json'] = json_data
        elif data is not None:
            request_kwargs['content'] = data
        
        # Retry logic
        last_exception = None
        for attempt in range(self.max_retries + 1):
            try:
                self._total_requests += 1
                
                response = await self._client.request(**request_kwargs)
                
                # Check for rate limiting
                if response.status_code == 429:
                    self._rate_limited_requests += 1
                    retry_after = self._get_retry_after(response)
                    
                    if attempt < self.max_retries:
                        self.logger.warning(f"Rate limited, retrying after {retry_after}s (attempt {attempt + 1})")
                        await asyncio.sleep(retry_after)
                        continue
                    else:
                        raise RateLimitError(
                            f"Rate limit exceeded after {self.max_retries} retries",
                            retry_after=retry_after
                        )
                
                # Check for other HTTP errors
                if response.status_code >= 400:
                    error_msg = f"HTTP {response.status_code}: {response.text}"
                    if attempt < self.max_retries and response.status_code >= 500:
                        # Retry on server errors
                        self.logger.warning(f"Server error, retrying (attempt {attempt + 1}): {error_msg}")
                        await asyncio.sleep(self.backoff_factor * (2 ** attempt))
                        continue
                    else:
                        self._failed_requests += 1
                        raise HTTPClientError(error_msg)
                
                self._successful_requests += 1
                self.logger.debug(f"Request successful: {method} {url} -> {response.status_code}")
                return response
                
            except (RequestError, TimeoutException, HTTPError) as e:
                last_exception = e
                if attempt < self.max_retries:
                    sleep_time = self.backoff_factor * (2 ** attempt)
                    self.logger.warning(f"Request failed, retrying in {sleep_time}s (attempt {attempt + 1}): {e}")
                    await asyncio.sleep(sleep_time)
                    continue
                else:
                    self._failed_requests += 1
                    break
        
        # All retries exhausted
        error_msg = f"Request failed after {self.max_retries} retries: {last_exception}"
        self.logger.error(error_msg)
        raise HTTPClientError(error_msg)
    
    def _get_retry_after(self, response: Response) -> int:
        """
        Extract retry-after value from rate limit response.
        
        Args:
            response: HTTP response
            
        Returns:
            Retry after seconds
        """
        # Check Retry-After header
        retry_after = response.headers.get('Retry-After')
        if retry_after:
            try:
                return int(retry_after)
            except ValueError:
                pass
        
        # Check X-RateLimit-Reset header (Unix timestamp)
        reset_time = response.headers.get('X-RateLimit-Reset')
        if reset_time:
            try:
                reset_timestamp = int(reset_time)
                current_timestamp = int(time.time())
                return max(1, reset_timestamp - current_timestamp)
            except ValueError:
                pass
        
        # Default retry after from configuration
        return self.rate_limit_retry_delay
    
    async def get(self, url: str, 
                  params: Optional[Dict[str, Any]] = None,
                  headers: Optional[Dict[str, str]] = None,
                  auth: Optional[tuple] = None) -> Response:
        """
        Make GET request.
        
        Args:
            url: Request URL
            params: Query parameters
            headers: Additional headers
            auth: Authentication tuple
            
        Returns:
            HTTP response
        """
        return await self._make_request('GET', url, params=params, headers=headers, auth=auth)
    
    async def post(self, url: str,
                   json_data: Optional[Dict[str, Any]] = None,
                   data: Optional[Union[str, bytes, Dict[str, Any]]] = None,
                   params: Optional[Dict[str, Any]] = None,
                   headers: Optional[Dict[str, str]] = None,
                   auth: Optional[tuple] = None) -> Response:
        """
        Make POST request.
        
        Args:
            url: Request URL
            json_data: JSON data
            data: Request data
            params: Query parameters
            headers: Additional headers
            auth: Authentication tuple
            
        Returns:
            HTTP response
        """
        return await self._make_request('POST', url, params=params, json_data=json_data, 
                                      data=data, headers=headers, auth=auth)
    
    async def put(self, url: str,
                  json_data: Optional[Dict[str, Any]] = None,
                  data: Optional[Union[str, bytes, Dict[str, Any]]] = None,
                  params: Optional[Dict[str, Any]] = None,
                  headers: Optional[Dict[str, str]] = None,
                  auth: Optional[tuple] = None) -> Response:
        """
        Make PUT request.
        
        Args:
            url: Request URL
            json_data: JSON data
            data: Request data
            params: Query parameters
            headers: Additional headers
            auth: Authentication tuple
            
        Returns:
            HTTP response
        """
        return await self._make_request('PUT', url, params=params, json_data=json_data,
                                      data=data, headers=headers, auth=auth)
    
    async def delete(self, url: str,
                     params: Optional[Dict[str, Any]] = None,
                     headers: Optional[Dict[str, str]] = None,
                     auth: Optional[tuple] = None) -> Response:
        """
        Make DELETE request.
        
        Args:
            url: Request URL
            params: Query parameters
            headers: Additional headers
            auth: Authentication tuple
            
        Returns:
            HTTP response
        """
        return await self._make_request('DELETE', url, params=params, headers=headers, auth=auth)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get client statistics.
        
        Returns:
            Dictionary with client statistics
        """
        success_rate = 0.0
        if self._total_requests > 0:
            success_rate = (self._successful_requests / self._total_requests) * 100
        
        return {
            'total_requests': self._total_requests,
            'successful_requests': self._successful_requests,
            'failed_requests': self._failed_requests,
            'rate_limited_requests': self._rate_limited_requests,
            'success_rate': round(success_rate, 2),
            'rate_limit_interval': self._min_request_interval,
            'base_url': self.base_url,
            'timeout': self.timeout,
            'max_retries': self.max_retries
        }