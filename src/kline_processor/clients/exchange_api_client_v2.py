"""
Exchange API client (v2) - matching TypeScript logic for K-line fetching.
"""

import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone

from ..models.kline import KlineData
from ..utils.logger import get_logger
from .http_client import HTTPClient
from ..config.settings import Settings


class ExchangeAPIError(Exception):
    """Base exception for exchange API errors."""
    pass


class RateLimitError(ExchangeAPIError):
    """Exception raised when API rate limit is exceeded."""

    def __init__(self, message: str, retry_after: Optional[int] = None):
        super().__init__(message)
        self.retry_after = retry_after


class InvalidParameterError(ExchangeAPIError):
    """Exception raised for invalid API parameters."""
    pass


class ExchangeAPIClientV2:
    """Exchange API client for fetching K-lines matching TypeScript implementation."""

    def __init__(self,
                 settings: Optional[Settings] = None,
                 http_client: Optional[HTTPClient] = None):
        """
        Initialize Exchange API client.

        Args:
            settings: Settings instance
            http_client: HTTP client instance
        """
        if settings is None:
            settings = Settings()
        self.base_url = settings.kline_url_v2
        # Don't pass base_url to HTTPClient, we'll use full URLs
        self.http_client = http_client or HTTPClient()
        self.logger = get_logger(self.__class__.__name__)
        
    async def __aenter__(self):
        """Async context manager entry."""
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
        
    async def close(self):
        """Close the client and cleanup resources."""
        try:
            await self.http_client.close()
            self.logger.info("Exchange API client v2 closed successfully")
        except Exception as e:
            self.logger.error(f"Error closing Exchange API client v2: {e}")
    
    async def get_continuous_klines(self,
                                   symbol: str,
                                   interval: str,
                                   limit: int = 1000,
                                   end_time: Optional[int] = None) -> List[KlineData]:
        """
        Get K-line data matching TypeScript logic.
        
        Args:
            symbol: Trading pair (e.g., 'BTCUSDT')
            interval: Time interval (e.g., '1m', '5m', '1h')
            limit: Number of K-lines to fetch (max 1500)
            end_time: End time in milliseconds
            
        Returns:
            List of K-line data
        """
        # Prepare parameters for standard klines endpoint
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        if end_time is not None:
            params['endTime'] = int(end_time)
        
        try:
            # Make request using standard klines endpoint with full URL
            full_url = f"{self.base_url}/fapi/v1/klines"
            response = await self.http_client.get(full_url, params=params)
            
            # Parse JSON response
            try:
                data = response.json()
            except Exception as e:
                self.logger.error(f"Failed to parse JSON response: {e}")
                raise ExchangeAPIError(f"Invalid JSON response: {e}")
            
            # Convert response to KlineData objects
            klines = []
            
            # Check if response is a dict with error
            if isinstance(data, dict) and 'code' in data:
                self.logger.error(f"API error: {data}")
                raise ExchangeAPIError(f"API error {data.get('code')}: {data.get('msg', 'Unknown error')}")
            
            # Parse the list of klines
            for item in data:
                try:
                    # Convert using same format as TypeScript convertKlineFormat
                    kline = self._convert_kline_format(symbol, interval, item)
                    klines.append(kline)
                except Exception as e:
                    self.logger.warning(f"Failed to parse K-line data: {e}")
                    continue
            
            self.logger.info(f"Fetched {len(klines)} continuous K-lines for {symbol}:{interval}")
            return klines
            
        except Exception as e:
            self.logger.error(f"Failed to fetch continuous K-lines for {symbol}:{interval}: {e}")
            raise
    
    def _convert_kline_format(self, symbol: str, interval: str, item: List[Any]) -> KlineData:
        """
        Convert K-line data to KlineData format matching TypeScript logic.
        
        Args:
            symbol: Trading symbol
            interval: K-line interval
            item: Raw K-line data array
            
        Returns:
            KlineData object
        """
        return KlineData(
            t=int(item[0]),        # 开盘时间
            T=int(item[6]),        # 收盘时间
            s=symbol,              # 交易对
            i=interval,            # K线间隔
            f=0,                   # 第一笔成交ID（原数据中没有，设为0）
            L=0,                   # 末一笔成交ID（原数据中没有，设为0）
            o=str(item[1]),        # 开盘价
            c=str(item[4]),        # 收盘价
            h=str(item[2]),        # 最高价
            l=str(item[3]),        # 最低价
            v=str(item[5]),        # 成交量
            n=int(item[8]),        # 成交笔数
            x=True,                # 是否完结（假设为true）
            q=str(item[7]),        # 成交额
            V=str(item[9]),        # 主动买入成交量
            Q=str(item[10]),       # 主动买入成交额
            B="0"                  # 忽略此参数
        )