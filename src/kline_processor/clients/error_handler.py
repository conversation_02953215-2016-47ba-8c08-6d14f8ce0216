"""
Enhanced error handling for exchange API interactions.
"""

from typing import Optional, Dict, Any
import asyncio
from ..utils.logger import get_logger


class ExchangeErrorHandler:
    """Handle various exchange API errors with appropriate strategies."""
    
    # Error codes that should not be retried
    NON_RETRYABLE_ERRORS = {
        -1121: "Invalid symbol",
        -1100: "Illegal characters found in parameter",
        -1101: "Too many parameters",
        -1102: "Mandatory parameter was not sent",
        -1103: "Unknown parameter sent",
        -1104: "Not all sent parameters were read",
        -1105: "Parameter empty",
        -1106: "Parameter not required",
        -1111: "Precision is over the maximum",
        -1112: "No orders on book",
        -1114: "TimeInForce parameter sent when not required",
        -1115: "Invalid timeInForce",
        -1116: "Invalid orderType",
        -1117: "Invalid side",
        -1118: "New client order ID was empty",
        -1119: "Original client order ID was empty",
        -1120: "Invalid interval",
        -1125: "Invalid listen key",
        -1127: "Lookup interval is too big",
        -1128: "Combination of optional parameters invalid",
    }
    
    # Error codes that can be retried with delay
    RETRYABLE_ERRORS = {
        -1000: ("Unknown error", 1),
        -1001: ("Internal error", 5),
        -1002: ("Unauthorized", 0),  # Need to check API key
        -1003: ("Too many requests", 60),  # Rate limit
        -1004: ("Server busy", 5),
        -1005: ("No such IP has been white listed", 0),
        -1006: ("An unexpected response", 1),
        -1007: ("Timeout", 1),
        -1010: ("ERROR_MSG_RECEIVED", 1),
        -1011: ("NON_WHITE_LIST", 0),
        -1013: ("Invalid message", 1),
        -1014: ("Unknown order composition", 1),
        -1015: ("Too many orders", 5),
        -1016: ("Service shutting down", 60),
        -1020: ("Unsupported operation", 0),
        -1021: ("Invalid timestamp", 0),
        -1022: ("Invalid signature", 0),
    }
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def should_retry(self, error_code: int) -> bool:
        """Check if error should trigger a retry."""
        return error_code in self.RETRYABLE_ERRORS
    
    def get_retry_delay(self, error_code: int) -> int:
        """Get retry delay in seconds for specific error code."""
        if error_code in self.RETRYABLE_ERRORS:
            return self.RETRYABLE_ERRORS[error_code][1]
        return 0
    
    def get_error_message(self, error_code: int) -> str:
        """Get human-readable error message."""
        if error_code in self.NON_RETRYABLE_ERRORS:
            return self.NON_RETRYABLE_ERRORS[error_code]
        elif error_code in self.RETRYABLE_ERRORS:
            return self.RETRYABLE_ERRORS[error_code][0]
        return f"Unknown error code: {error_code}"
    
    async def handle_api_error(self, error_data: Dict[str, Any], retry_count: int = 0, max_retries: int = 3) -> bool:
        """
        Handle API error response.
        
        Args:
            error_data: Error response from API
            retry_count: Current retry attempt
            max_retries: Maximum retry attempts
            
        Returns:
            True if should retry, False otherwise
        """
        error_code = error_data.get('code', 0)
        error_msg = error_data.get('msg', 'Unknown error')
        
        # Log the error
        self.logger.error(f"API Error {error_code}: {error_msg}")
        
        # Check if error is retryable
        if not self.should_retry(error_code):
            self.logger.error(f"Non-retryable error: {self.get_error_message(error_code)}")
            return False
        
        # Check retry count
        if retry_count >= max_retries:
            self.logger.error(f"Max retries ({max_retries}) exceeded for error {error_code}")
            return False
        
        # Get retry delay
        retry_delay = self.get_retry_delay(error_code)
        if retry_delay > 0:
            self.logger.info(f"Will retry after {retry_delay}s (attempt {retry_count + 1}/{max_retries})")
            await asyncio.sleep(retry_delay)
        
        return True
    
    def validate_symbol(self, symbol: str) -> bool:
        """
        Validate trading symbol format.
        
        Args:
            symbol: Trading symbol to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not symbol or not isinstance(symbol, str):
            return False
        
        # Check if symbol matches expected pattern (e.g., BTCUSDT)
        # Binance symbols are typically uppercase letters only
        if not symbol.isupper() or not symbol.isalpha():
            self.logger.warning(f"Invalid symbol format: {symbol}")
            return False
        
        # Check minimum length (e.g., BTCUSDT has 7 chars minimum)
        if len(symbol) < 6:
            self.logger.warning(f"Symbol too short: {symbol}")
            return False
        
        return True
    
    def validate_interval(self, interval: str) -> bool:
        """
        Validate time interval format.
        
        Args:
            interval: Time interval to validate
            
        Returns:
            True if valid, False otherwise
        """
        valid_intervals = {
            '1m', '3m', '5m', '15m', '30m',
            '1h', '2h', '4h', '6h', '8h', '12h',
            '1d', '3d', '1w', '1M'
        }
        
        if interval not in valid_intervals:
            self.logger.warning(f"Invalid interval: {interval}")
            return False
        
        return True