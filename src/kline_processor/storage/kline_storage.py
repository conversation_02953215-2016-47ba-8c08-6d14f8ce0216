"""
K-line specific Redis storage operations and pub/sub functionality.

This module provides specialized storage operations for K-line data,
including time-series storage, pub/sub messaging, and data retrieval.
"""

import asyncio
from typing import List, Dict, Any, Optional, AsyncIterator, Set
from datetime import datetime, timedelta, timezone
import json
import os

import redis.asyncio as redis
from redis.asyncio.client import PubSub

from .redis_client import RedisClient
from ..models.kline import KlineData, ValidatedKlineData
from ..utils.converter import DataConverter
from ..utils.timeframe import TimeframeUtils
from ..utils.logger import get_logger


class KlineStorageError(Exception):
    """Custom exception for K-line storage operations."""
    pass


class KlineStorage:
    """
    K-line specific storage operations using Redis.
    
    This class provides high-level operations for storing, retrieving,
    and subscribing to K-line data using Redis sorted sets and pub/sub.
    """
    
    def __init__(self, redis_client: RedisClient = None):
        """
        Initialize KlineStorage with Redis client.
        
        Args:
            redis_client: Redis client instance (creates new instance if None)
        """
        self.redis_client = redis_client or RedisClient()
        self.logger = get_logger(self.__class__.__name__)
        
        # Key patterns
        self.kline_key_pattern = "kline:{symbol}:{interval}"
        self.latest_key_pattern = "latest:{symbol}:{interval}"
        self.meta_key_pattern = "meta:{symbol}:{interval}"
        
        # Pub/Sub channels
        self.kline_channel_pattern = "kline_updates:{symbol}:{interval}"
        self.global_channel = "kline_updates:*"
        
        # Storage configuration
        # Import settings to make limit configurable
        from ..config.settings import Settings
        settings = Settings()
        self.max_klines_per_symbol = int(os.getenv("MAX_KLINES_PER_SYMBOL", "15000"))  # Configurable limit
        self.cleanup_batch_size = 1000      # Batch size for cleanup operations
        self.expiry_days = 300               # Expiry for metadata keys
    
    def _get_kline_key(self, symbol: str, interval: str) -> str:
        """Generate Redis key for K-line data storage."""
        return self.kline_key_pattern.format(symbol=symbol.upper(), interval=interval)
    
    def _get_latest_key(self, symbol: str, interval: str) -> str:
        """Generate Redis key for latest K-line data."""
        return self.latest_key_pattern.format(symbol=symbol.upper(), interval=interval)
    
    def _get_meta_key(self, symbol: str, interval: str) -> str:
        """Generate Redis key for metadata."""
        return self.meta_key_pattern.format(symbol=symbol.upper(), interval=interval)
    
    def _get_kline_channel(self, symbol: str, interval: str) -> str:
        """Generate pub/sub channel for K-line updates."""
        return self.kline_channel_pattern.format(symbol=symbol.upper(), interval=interval)
    
    async def store_kline(self, kline: KlineData, publish: bool = True) -> bool:
        """
        Store K-line data in Redis with optional pub/sub notification.
        
        Args:
            kline: K-line data to store
            publish: Whether to publish update notification
            
        Returns:
            True if successful, False otherwise
        """
        try:
            key = self._get_kline_key(kline.s, kline.i)
            latest_key = self._get_latest_key(kline.s, kline.i)
            meta_key = self._get_meta_key(kline.s, kline.i)
            
            # Convert to storage format
            storage_data = DataConverter.format_for_storage(kline)
            storage_json = json.dumps(storage_data, default=str)
            
            # Use timestamp as score for sorted set
            score = float(kline.t)
            
            # Store in sorted set (time-series)
            await self.redis_client.zadd(key, {storage_json: score})
            
            # Update latest K-line with protection against overwriting final with non-final
            can_update_latest = True
            try:
                existing_latest_raw = await self.redis_client.get(latest_key)
                if existing_latest_raw:
                    existing_latest = existing_latest_raw
                    # existing_latest_raw may already be a JSON string; normalize to dict
                    try:
                        existing_latest = json.loads(existing_latest_raw)
                    except Exception:
                        existing_latest = None
                    if isinstance(existing_latest, dict):
                        if existing_latest.get('x') is True and kline.x is False:
                            can_update_latest = False
            except Exception:
                # if any error occurs during protection logic, proceed to update to avoid stale cache
                can_update_latest = True

            if can_update_latest:
                await self.redis_client.set(latest_key, storage_json)
            
            # Update metadata
            metadata = {
                'symbol': kline.s,
                'interval': kline.i,
                'last_update': datetime.now(timezone.utc).isoformat(),
                'latest_timestamp': kline.t,
                'latest_close_time': kline.T,
                'is_closed': kline.x
            }
            await self.redis_client.set(meta_key, metadata, expire=self.expiry_days * 24 * 3600)
            
            # Cleanup old data if needed
            await self._cleanup_old_data(key)
            
            # Publish update notification
            if publish:
                await self._publish_kline_update(kline)
            
            self.logger.debug(f"Stored K-line for {kline.s}:{kline.i} at {kline.t}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store K-line for {kline.s}:{kline.i}: {e}")
            return False
    
    async def store_klines_batch(self, klines: List[KlineData], publish: bool = True) -> int:
        """
        Store multiple K-lines in batch for better performance.
        
        Args:
            klines: List of K-line data to store
            publish: Whether to publish update notifications
            
        Returns:
            Number of successfully stored K-lines
        """
        if not klines:
            return 0
        
        stored_count = 0
        grouped_klines = {}
        
        # Group K-lines by symbol and interval
        for kline in klines:
            key = f"{kline.s}:{kline.i}"
            if key not in grouped_klines:
                grouped_klines[key] = []
            grouped_klines[key].append(kline)
        
        try:
            # Process each group
            for group_key, group_klines in grouped_klines.items():
                symbol, interval = group_key.split(':')
                redis_key = self._get_kline_key(symbol, interval)
                
                # Group K-lines by timestamp to handle updates correctly  
                timestamp_groups = {}
                for kline in group_klines:
                    timestamp = kline.t
                    if timestamp not in timestamp_groups:
                        timestamp_groups[timestamp] = []
                    timestamp_groups[timestamp].append(kline)
                
                # For each timestamp, select the canonical K-line using idempotent selector:
                # priority: is_final(x=True) > larger last trade id (L) > larger close time (T)
                final_klines = {}
                for timestamp, klines in timestamp_groups.items():
                    def _score(k):
                        # bool True>False in Python, so (k.x, k.L, k.T) works as desired
                        return (bool(k.x), int(k.L), int(k.T))
                    latest_kline = max(klines, key=_score)
                    storage_data = DataConverter.format_for_storage(latest_kline)
                    storage_json = json.dumps(storage_data, default=str)
                    score = float(timestamp)

                    # Remove any existing K-line for this timestamp first (upsert semantics)
                    await self.redis_client.zremrangebyscore(redis_key, score, score)

                    # Add the canonical K-line for this timestamp
                    await self.redis_client.zadd(redis_key, {storage_json: score})
                    stored_count += 1
                    final_klines[timestamp] = latest_kline

                # Update latest K-line (use the most recent timestamp) with protection:
                # do not allow non-final to overwrite an existing final latest
                if final_klines:
                    latest_timestamp = max(final_klines.keys())
                    latest_kline = final_klines[latest_timestamp]
                    latest_key = self._get_latest_key(symbol, interval)

                    # read current latest for protection
                    existing_latest_raw = await self.redis_client.get(latest_key)
                    can_update_latest = True
                    if existing_latest_raw:
                        try:
                            existing_latest = json.loads(existing_latest_raw)
                            # existing_latest is in storage format; field x is expected under ['x']
                            if existing_latest.get('x') is True and latest_kline.x is False:
                                # keep final in cache; still ZSET has been updated above
                                can_update_latest = False
                        except Exception:
                            # if parse fails, allow update to self-heal cache
                            can_update_latest = True

                    if can_update_latest:
                        latest_storage = DataConverter.format_for_storage(latest_kline)
                        await self.redis_client.set(latest_key, json.dumps(latest_storage, default=str))
                    
                    # Cleanup old data
                    await self._cleanup_old_data(redis_key)
                    
                    # Publish notifications (only for final K-lines to avoid spam)
                    if publish:
                        for kline in final_klines.values():
                            await self._publish_kline_update(kline)
            
            # self.logger.info(f"Batch stored {stored_count} K-lines")
            return stored_count
            
        except Exception as e:
            self.logger.error(f"Failed to batch store K-lines: {e}")
            return stored_count
    
    async def get_klines(self, symbol: str, interval: str, 
                        start_time: Optional[int] = None, 
                        end_time: Optional[int] = None,
                        limit: Optional[int] = None) -> List[KlineData]:
        """
        Retrieve K-lines from Redis within time range.
        
        Args:
            symbol: Trading symbol
            interval: Time interval
            start_time: Start timestamp (inclusive)
            end_time: End timestamp (inclusive)
            limit: Maximum number of results
            
        Returns:
            List of K-line data sorted by timestamp
        """
        try:
            key = self._get_kline_key(symbol, interval)
            
            # Determine score range
            min_score = float(start_time) if start_time else "-inf"
            max_score = float(end_time) if end_time else "+inf"
            
            # Get data from sorted set
            if limit:
                # Use ZRANGEBYSCORE with LIMIT
                results = await self.redis_client.zrangebyscore(
                    key, min_score, max_score, start=0, num=limit
                )
            else:
                results = await self.redis_client.zrangebyscore(key, min_score, max_score)
            
            # Convert back to KlineData objects
            klines = []
            for result in results:
                try:
                    storage_data = json.loads(result)
                    kline = DataConverter.parse_from_storage(storage_data)
                    if kline:
                        klines.append(kline)
                except (json.JSONDecodeError, Exception) as e:
                    self.logger.warning(f"Failed to parse stored K-line data: {e}")
            
            self.logger.debug(f"Retrieved {len(klines)} K-lines for {symbol}:{interval}")
            return klines
            
        except Exception as e:
            self.logger.error(f"Failed to get K-lines for {symbol}:{interval}: {e}")
            return []
    
    async def get_latest_kline(self, symbol: str, interval: str) -> Optional[KlineData]:
        """
        Get the latest K-line for a symbol and interval.
        
        Args:
            symbol: Trading symbol
            interval: Time interval
            
        Returns:
            Latest K-line data or None if not found
        """
        try:
            key = self._get_latest_key(symbol, interval)
            result = await self.redis_client.get(key)
            
            if result:
                storage_data = json.loads(result)
                return DataConverter.parse_from_storage(storage_data)
            
            # Fallback to zrevrange when latest cache missing
            zkey = self._get_kline_key(symbol, interval)
            last = await self.redis_client.zrange(zkey, -1, -1)
            if last:
                try:
                    storage_data = json.loads(last[0])
                    return DataConverter.parse_from_storage(storage_data)
                except Exception:
                    pass
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get latest K-line for {symbol}:{interval}: {e}")
            return None
    
    async def get_kline_count(self, symbol: str, interval: str) -> int:
        """
        Get the number of stored K-lines for a symbol and interval.
        
        Args:
            symbol: Trading symbol
            interval: Time interval
            
        Returns:
            Number of stored K-lines
        """
        try:
            key = self._get_kline_key(symbol, interval)
            return await self.redis_client.zcard(key)
        except Exception as e:
            self.logger.error(f"Failed to get K-line count for {symbol}:{interval}: {e}")
            return 0
    
    async def delete_klines(self, symbol: str, interval: str,
                           start_time: Optional[int] = None,
                           end_time: Optional[int] = None) -> int:
        """
        Delete K-lines within time range.
        
        Args:
            symbol: Trading symbol
            interval: Time interval
            start_time: Start timestamp (inclusive)
            end_time: End timestamp (inclusive)
            
        Returns:
            Number of deleted K-lines
        """
        try:
            key = self._get_kline_key(symbol, interval)
            
            if start_time is None and end_time is None:
                # Delete all K-lines for this symbol/interval
                count = await self.redis_client.zcard(key)
                await self.redis_client.delete(key)
                
                # Also delete related keys
                latest_key = self._get_latest_key(symbol, interval)
                meta_key = self._get_meta_key(symbol, interval)
                await self.redis_client.delete(latest_key, meta_key)
                
                return count
            else:
                # Delete by score range
                min_score = float(start_time) if start_time else "-inf"
                max_score = float(end_time) if end_time else "+inf"
                
                # First get count
                count_results = await self.redis_client.zrangebyscore(key, min_score, max_score)
                count = len(count_results)
                
                # Then remove by score range
                await self.redis_client.zremrangebyscore(key, min_score, max_score)
                
                return count
                
        except Exception as e:
            self.logger.error(f"Failed to delete K-lines for {symbol}:{interval}: {e}")
            return 0

    async def delete_tail(self, symbol: str, interval: str, count: int) -> int:
        """
        Delete last N K-lines (most recent first) for a symbol/interval.
        Returns deleted count.
        """
        if count <= 0:
            return 0
        try:
            key = self._get_kline_key(symbol, interval)
            # Fetch last N elements
            items = await self.redis_client.zrange(key, -count, -1)
            deleted = 0
            if items:
                # Determine their exact scores to delete precise timestamps
                # Since we store JSON as member and score is timestamp,
                # use zremrangebyrank to drop last N directly.
                await self.redis_client.zremrangebyrank(key, -len(items), -1)
                deleted = len(items)
            # Invalidate latest cache so it can be rebuilt on next store
            latest_key = self._get_latest_key(symbol, interval)
            await self.redis_client.delete(latest_key)
            self.logger.info(f"Deleted tail {deleted} klines for {symbol}:{interval}")
            return deleted
        except Exception as e:
            self.logger.error(f"Failed to delete tail klines for {symbol}:{interval}: {e}")
            return 0
    
    async def remove_kline_by_timestamp(self, symbol: str, interval: str, timestamp: int) -> bool:
        """
        Remove K-line data for specific timestamp.
        
        This method implements the remove part of the remove-then-insert pattern,
        ensuring that we can atomically replace K-line data at specific timestamps.
        
        Args:
            symbol: Trading symbol
            interval: K-line interval
            timestamp: K-line open time in milliseconds
            
        Returns:
            True if K-line was removed, False if no K-line found at timestamp
        """
        try:
            key = self._get_kline_key(symbol, interval)
            
            # Find K-lines with exact timestamp (score = timestamp)
            members = await self.redis_client.zrangebyscore(key, timestamp, timestamp)
            removed_count = 0
            
            if members:
                # Remove each member found at this timestamp
                for member in members:
                    result = await self.redis_client.zrem(key, member)
                    if result:
                        removed_count += 1
                
                if removed_count > 0:
                    self.logger.debug(f"Removed {removed_count} K-line(s) for {symbol}:{interval} at timestamp {timestamp}")
                    
                    # Check if we need to invalidate the latest cache
                    # Get the current latest timestamp in the set
                    latest_members = await self.redis_client.zrange(key, -1, -1, withscores=True)
                    if not latest_members or (latest_members and latest_members[0][1] < timestamp):
                        # The removed K-line was the latest, invalidate cache
                        latest_key = self._get_latest_key(symbol, interval)
                        await self.redis_client.delete(latest_key)
                        self.logger.debug(f"Invalidated latest cache for {symbol}:{interval} after removing latest K-line")
                    
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to remove K-line for {symbol}:{interval} at {timestamp}: {e}")
            return False
    
    async def remove_klines_batch(self, removals: List[tuple]) -> int:
        """
        Batch remove K-lines for multiple timestamp/symbol/interval combinations.
        
        This method provides efficient batch removal for multiple K-lines,
        reducing Redis round trips when processing multiple removals.
        
        Args:
            removals: List of tuples (symbol, interval, timestamp)
            
        Returns:
            Number of K-lines successfully removed
        """
        if not removals:
            return 0
        
        removed_count = 0
        
        try:
            # Group removals by symbol:interval for efficiency
            removal_groups = {}
            for symbol, interval, timestamp in removals:
                key = f"{symbol}:{interval}"
                if key not in removal_groups:
                    removal_groups[key] = []
                removal_groups[key].append(timestamp)
            
            # Process each group
            for group_key, timestamps in removal_groups.items():
                symbol, interval = group_key.split(':')
                redis_key = self._get_kline_key(symbol, interval)
                
                # Remove all timestamps in this group
                for timestamp in timestamps:
                    members = await self.redis_client.zrangebyscore(redis_key, timestamp, timestamp)
                    if members:
                        for member in members:
                            result = await self.redis_client.zrem(redis_key, member)
                            if result:
                                removed_count += 1
                
                # Check if we need to invalidate latest cache for this symbol:interval
                if timestamps:
                    max_removed_timestamp = max(timestamps)
                    latest_members = await self.redis_client.zrange(redis_key, -1, -1, withscores=True)
                    if not latest_members or (latest_members and latest_members[0][1] < max_removed_timestamp):
                        latest_key = self._get_latest_key(symbol, interval)
                        await self.redis_client.delete(latest_key)
            
            if removed_count > 0:
                self.logger.info(f"Batch removed {removed_count} K-lines from {len(removals)} removal requests")
            
            return removed_count
            
        except Exception as e:
            self.logger.error(f"Failed to batch remove K-lines: {e}")
            return removed_count
    
    async def _cleanup_old_data(self, key: str) -> None:
        """Clean up old K-line data to maintain storage limits."""
        try:
            count = await self.redis_client.zcard(key)
            if count > self.max_klines_per_symbol:
                # Remove oldest entries
                excess_count = count - self.max_klines_per_symbol
                await self.redis_client.zremrangebyrank(key, 0, excess_count - 1)
                self.logger.debug(f"Cleaned up {excess_count} old K-lines from {key}")
        except Exception as e:
            self.logger.warning(f"Failed to cleanup old data for {key}: {e}")
    
    async def _publish_kline_update(self, kline: KlineData) -> None:
        """Publish K-line update notification."""
        try:
            channel = self._get_kline_channel(kline.s, kline.i)
            message = {
                'type': 'kline_update',
                'symbol': kline.s,
                'interval': kline.i,
                'timestamp': kline.t,
                'is_closed': kline.x,
                # minimal audit fields for external verification
                'audit': {
                    'finalize_reason': 'inherited' if kline.x else 'update',
                    'group_min_ts': kline.t,
                    'group_max_ts': kline.T,
                    'source': 'derived' if hasattr(kline, 'i') else 'unknown'
                },
                'data': DataConverter.format_for_storage(kline)
            }
            
            await self.redis_client.publish(channel, message)
            await self.redis_client.publish(self.global_channel, message)
            
        except Exception as e:
            self.logger.warning(f"Failed to publish K-line update: {e}")
    
    async def subscribe_kline_updates(self, symbol: str = None, interval: str = None) -> AsyncIterator[Dict[str, Any]]:
        """
        Subscribe to K-line updates.
        
        Args:
            symbol: Specific symbol to subscribe to (None for all)
            interval: Specific interval to subscribe to (None for all)
            
        Yields:
            Dictionary with update information
        """
        try:
            # Create pubsub instance
            async with self.redis_client._get_connection() as conn:
                pubsub = conn.pubsub()
                
                # Subscribe to appropriate channels
                if symbol and interval:
                    channel = self._get_kline_channel(symbol, interval)
                    await pubsub.subscribe(channel)
                else:
                    # Subscribe to pattern for all updates
                    await pubsub.psubscribe("kline_updates:*")
                
                self.logger.info(f"Subscribed to K-line updates: {symbol}:{interval}")
                
                try:
                    async for message in pubsub.listen():
                        if message['type'] in ['message', 'pmessage']:
                            try:
                                data = json.loads(message['data'])
                                yield data
                            except (json.JSONDecodeError, Exception) as e:
                                self.logger.warning(f"Failed to parse pub/sub message: {e}")
                                
                except asyncio.CancelledError:
                    self.logger.info("K-line subscription cancelled")
                    
                finally:
                    await pubsub.unsubscribe()
                    await pubsub.aclose()
                    
        except Exception as e:
            self.logger.error(f"Failed to subscribe to K-line updates: {e}")
    
    async def get_symbols_with_data(self) -> Set[str]:
        """
        Get all symbols that have K-line data stored.
        
        Returns:
            Set of symbols
        """
        try:
            pattern = "kline:*"
            keys = await self.redis_client.keys(pattern)
            
            symbols = set()
            for key in keys:
                # Extract symbol from key: "kline:BTCUSDT:1m" -> "BTCUSDT"
                parts = key.split(':')
                if len(parts) >= 2:
                    symbols.add(parts[1])
            
            return symbols
            
        except Exception as e:
            self.logger.error(f"Failed to get symbols with data: {e}")
            return set()
    
    async def get_intervals_for_symbol(self, symbol: str) -> Set[str]:
        """
        Get all intervals that have data for a specific symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Set of intervals
        """
        try:
            pattern = f"kline:{symbol.upper()}:*"
            keys = await self.redis_client.keys(pattern)
            
            intervals = set()
            for key in keys:
                # Extract interval from key: "kline:BTCUSDT:1m" -> "1m"
                parts = key.split(':')
                if len(parts) >= 3:
                    intervals.add(parts[2])
            
            return intervals
            
        except Exception as e:
            self.logger.error(f"Failed to get intervals for symbol {symbol}: {e}")
            return set()
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics.
        
        Returns:
            Dictionary with storage statistics
        """
        try:
            stats = {
                'total_symbols': 0,
                'total_intervals': 0,
                'total_klines': 0,
                'symbol_stats': {},
                'storage_size_estimate': 0
            }
            
            # Get all K-line keys
            pattern = "kline:*"
            keys = await self.redis_client.keys(pattern)
            
            symbol_intervals = {}
            for key in keys:
                parts = key.split(':')
                if len(parts) >= 3:
                    symbol = parts[1]
                    interval = parts[2]
                    
                    if symbol not in symbol_intervals:
                        symbol_intervals[symbol] = set()
                    symbol_intervals[symbol].add(interval)
                    
                    # Get K-line count for this key
                    count = await self.redis_client.zcard(key)
                    stats['total_klines'] += count
                    
                    if symbol not in stats['symbol_stats']:
                        stats['symbol_stats'][symbol] = {}
                    
                    stats['symbol_stats'][symbol][interval] = {
                        'kline_count': count,
                        'key': key
                    }
            
            stats['total_symbols'] = len(symbol_intervals)
            stats['total_intervals'] = sum(len(intervals) for intervals in symbol_intervals.values())
            
            # Estimate storage size (rough approximation)
            stats['storage_size_estimate'] = stats['total_klines'] * 500  # ~500 bytes per K-line
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get storage stats: {e}")
            return {}
    
    async def cleanup_expired_data(self, days_to_keep: int = 30) -> int:
        """
        Clean up K-line data older than specified days.
        
        Args:
            days_to_keep: Number of days of data to keep
            
        Returns:
            Number of K-lines deleted
        """
        try:
            cutoff_time = int((datetime.now(timezone.utc) - timedelta(days=days_to_keep)).timestamp() * 1000)
            deleted_count = 0
            
            # Get all K-line keys
            pattern = "kline:*"
            keys = await self.redis_client.keys(pattern)
            
            for key in keys:
                # Delete old data by score (timestamp)
                count_before = await self.redis_client.zcard(key)
                await self.redis_client.zremrangebyscore(key, "-inf", cutoff_time)
                count_after = await self.redis_client.zcard(key)
                
                deleted_this_key = count_before - count_after
                deleted_count += deleted_this_key
                
                if deleted_this_key > 0:
                    self.logger.debug(f"Deleted {deleted_this_key} old K-lines from {key}")
            
            self.logger.info(f"Cleanup completed: deleted {deleted_count} old K-lines")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup expired data: {e}")
            return 0


# Global KlineStorage instance
kline_storage = KlineStorage()