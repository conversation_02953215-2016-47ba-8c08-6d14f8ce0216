"""
Enhanced logging system with file output, rotation, and detailed sync logging.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, Optional, Union
import structlog
from structlog.typing import FilteringBoundLogger
import json


class DetailedFormatter(logging.Formatter):
    """Custom formatter for detailed logging with context."""
    
    def format(self, record):
        # Add timestamp if not present
        if not hasattr(record, 'timestamp'):
            record.timestamp = datetime.utcnow().isoformat()
        
        # Format the base message
        formatted = super().format(record)
        
        # Add extra context if present
        if hasattr(record, 'extra_context') and record.extra_context:
            context_str = json.dumps(record.extra_context, default=str, separators=(',', ':'))
            formatted += f" | Context: {context_str}"
        
        return formatted


class SyncLogger:
    """Specialized logger for K-line synchronization with detailed tracking."""
    
    def __init__(self, logger: FilteringBoundLogger):
        self.logger = logger
        self._sync_stats = {
            'total_fetched': 0,
            'total_stored': 0,
            'total_errors': 0,
            'start_time': None,
            'current_iteration': 0
        }
    
    def start_sync(self, symbol: str, interval: str, total_iterations: int, target_per_iteration: int):
        """Log sync start with parameters."""
        self._sync_stats['start_time'] = datetime.utcnow()
        self._sync_stats['total_fetched'] = 0
        self._sync_stats['total_stored'] = 0
        self._sync_stats['total_errors'] = 0
        self._sync_stats['current_iteration'] = 0
        
        self.logger.info(
            "🚀 Starting K-line synchronization",
            symbol=symbol,
            interval=interval,
            total_iterations=total_iterations,
            target_per_iteration=target_per_iteration,
            expected_total=total_iterations * target_per_iteration,
            sync_id=f"{symbol}:{interval}:{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        )
    
    def log_iteration_start(self, iteration: int, symbol: str, interval: str, end_time: Optional[int] = None):
        """Log iteration start."""
        self._sync_stats['current_iteration'] = iteration
        
        self.logger.info(
            f"🔄 Starting iteration {iteration}",
            symbol=symbol,
            interval=interval,
            iteration=iteration,
            end_time=end_time,
            cumulative_fetched=self._sync_stats['total_fetched'],
            cumulative_stored=self._sync_stats['total_stored']
        )
    
    def log_fetch_result(self, symbol: str, interval: str, iteration: int, 
                        fetched_count: int, requested_count: int, 
                        oldest_timestamp: Optional[int] = None, 
                        newest_timestamp: Optional[int] = None):
        """Log data fetch results with details."""
        self._sync_stats['total_fetched'] += fetched_count
        
        self.logger.info(
            f"📥 Fetched {fetched_count}/{requested_count} K-lines",
            symbol=symbol,
            interval=interval,
            iteration=iteration,
            fetched_count=fetched_count,
            requested_count=requested_count,
            oldest_timestamp=oldest_timestamp,
            newest_timestamp=newest_timestamp,
            cumulative_fetched=self._sync_stats['total_fetched'],
            fetch_efficiency=f"{(fetched_count/requested_count)*100:.1f}%" if requested_count > 0 else "0%"
        )
    
    def log_storage_progress(self, symbol: str, interval: str, iteration: int,
                           stored_count: int, batch_size: int, total_in_iteration: int):
        """Log storage progress within an iteration."""
        self._sync_stats['total_stored'] += stored_count
        
        progress_pct = (stored_count / total_in_iteration * 100) if total_in_iteration > 0 else 0
        
        self.logger.info(
            f"💾 Stored {stored_count} K-lines (batch: {batch_size})",
            symbol=symbol,
            interval=interval,
            iteration=iteration,
            stored_count=stored_count,
            batch_size=batch_size,
            iteration_progress=f"{progress_pct:.1f}%",
            cumulative_stored=self._sync_stats['total_stored']
        )
    
    def log_iteration_complete(self, symbol: str, interval: str, iteration: int,
                             iteration_fetched: int, iteration_stored: int,
                             next_end_time: Optional[int] = None):
        """Log iteration completion."""
        self.logger.info(
            f"✅ Iteration {iteration} completed",
            symbol=symbol,
            interval=interval,
            iteration=iteration,
            iteration_fetched=iteration_fetched,
            iteration_stored=iteration_stored,
            next_end_time=next_end_time,
            cumulative_fetched=self._sync_stats['total_fetched'],
            cumulative_stored=self._sync_stats['total_stored']
        )
    
    def log_sync_complete(self, symbol: str, interval: str, total_iterations: int):
        """Log sync completion with summary."""
        duration = None
        if self._sync_stats['start_time']:
            duration = (datetime.utcnow() - self._sync_stats['start_time']).total_seconds()
        
        self.logger.info(
            "🎉 K-line synchronization completed",
            symbol=symbol,
            interval=interval,
            total_iterations=total_iterations,
            total_fetched=self._sync_stats['total_fetched'],
            total_stored=self._sync_stats['total_stored'],
            total_errors=self._sync_stats['total_errors'],
            duration_seconds=duration,
            avg_per_iteration=self._sync_stats['total_stored'] / total_iterations if total_iterations > 0 else 0
        )
    
    def log_sync_error(self, symbol: str, interval: str, iteration: int, error: Exception):
        """Log sync errors with context."""
        self._sync_stats['total_errors'] += 1
        
        self.logger.error(
            f"❌ Sync error in iteration {iteration}",
            symbol=symbol,
            interval=interval,
            iteration=iteration,
            error_type=type(error).__name__,
            error_message=str(error),
            cumulative_errors=self._sync_stats['total_errors']
        )


def setup_enhanced_logging(settings) -> tuple[FilteringBoundLogger, SyncLogger]:
    """
    Set up enhanced logging with file output and detailed sync logging.
    
    Args:
        settings: Application settings
        
    Returns:
        Tuple of (main_logger, sync_logger)
    """
    # Create logs directory
    log_dir = Path(settings.log_dir)
    log_dir.mkdir(exist_ok=True)
    
    # Create date-based subdirectory
    today = datetime.now().strftime('%Y-%m-%d')
    daily_log_dir = log_dir / today
    daily_log_dir.mkdir(exist_ok=True)
    
    # Configure standard library logging
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # Clear existing handlers
    root_logger.handlers.clear()

    # Set third-party library loggers to WARNING level to reduce noise
    third_party_loggers = [
        'httpx', 'httpcore', 'websockets', 'asyncio',
        'urllib3', 'requests', 'aiohttp'
    ]
    for logger_name in third_party_loggers:
        logging.getLogger(logger_name).setLevel(logging.WARNING)
    
    # File handler with rotation
    if settings.log_enable_file:
        file_handler = logging.handlers.RotatingFileHandler(
            filename=daily_log_dir / "kline_processor.log",
            maxBytes=_parse_size(settings.log_file_max_size),
            backupCount=settings.log_file_backup_count
        )
        file_handler.setLevel(getattr(logging, settings.log_file_level))
        file_formatter = structlog.stdlib.ProcessorFormatter(
            processor=structlog.dev.ConsoleRenderer(colors=False),
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    # Console handler (errors only)
    if settings.log_enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, settings.log_console_level))
        console_formatter = structlog.stdlib.ProcessorFormatter(
            processor=structlog.dev.ConsoleRenderer(colors=True),
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
    
    # Configure structlog to use standard library logging
    processors = [
        structlog.stdlib.filter_by_level,  # Filter by log level
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
    ]

    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(logging.DEBUG),
        logger_factory=structlog.stdlib.LoggerFactory(),  # Use stdlib logging instead of print
        cache_logger_on_first_use=True,
    )
    
    # Create loggers
    main_logger = structlog.get_logger("kline_processor")
    sync_logger = SyncLogger(structlog.get_logger("sync"))
    
    return main_logger, sync_logger


def _parse_size(size_str: str) -> int:
    """Parse size string like '10MB' to bytes."""
    size_str = size_str.upper()
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


def get_enhanced_logger(name: str = None) -> FilteringBoundLogger:
    """Get an enhanced logger instance."""
    return structlog.get_logger(name) if name else structlog.get_logger()
