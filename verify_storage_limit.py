#!/usr/bin/env python3
"""
Verify the K-line storage limit configuration.
"""

import asyncio
import os
from src.kline_processor.storage.kline_storage import KlineStorage
from src.kline_processor.config.settings import Settings

async def main():
    print("🔍 Verifying K-line Storage Configuration")
    print("=" * 50)
    
    # Check environment variable
    max_klines_env = os.getenv("MAX_KLINES_PER_SYMBOL", "Not set")
    print(f"Environment variable MAX_KLINES_PER_SYMBOL: {max_klines_env}")
    
    # Create storage instance and check the limit
    storage = KlineStorage()
    print(f"Storage max_klines_per_symbol: {storage.max_klines_per_symbol}")
    
    # Check current count in Redis
    count = await storage.get_kline_count("BTCUSDT", "1m")
    print(f"Current BTCUSDT:1m count in Redis: {count}")
    
    # Calculate expected vs actual
    settings = Settings()
    expected_total = settings.historical_batch_size * settings.startup_backfill_iterations
    print(f"\nExpected total from config: {expected_total}")
    print(f"  - HISTORICAL_BATCH_SIZE: {settings.historical_batch_size}")
    print(f"  - STARTUP_BACKFILL_ITERATIONS: {settings.startup_backfill_iterations}")
    
    print("\n✅ Summary:")
    if storage.max_klines_per_symbol >= expected_total:
        print(f"Storage limit ({storage.max_klines_per_symbol}) is sufficient for expected data ({expected_total})")
    else:
        print(f"⚠️  Storage limit ({storage.max_klines_per_symbol}) is less than expected data ({expected_total})")
        print(f"   Only the most recent {storage.max_klines_per_symbol} K-lines will be retained")

if __name__ == "__main__":
    asyncio.run(main())